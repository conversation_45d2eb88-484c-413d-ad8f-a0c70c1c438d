import axios from 'axios';
import { ChatSettings, Message, ApiError } from '../types';

export class AIService {
  private settings: ChatSettings;

  constructor(settings: ChatSettings) {
    this.settings = settings;
  }

  updateSettings(settings: ChatSettings) {
    this.settings = settings;
  }

  async sendMessage(messages: Message[]): Promise<string> {
    try {
      // Convert messages to the format expected by the API
      const apiMessages = messages.map(msg => ({
        role: msg.role,
        content: msg.content,
      }));

      // CHUTS AI API endpoint (assuming OpenAI-compatible format)
      const response = await axios.post(
        'https://api.chuts.ai/v1/chat/completions',
        {
          model: this.settings.model,
          messages: apiMessages,
          max_tokens: 2000,
          temperature: 0.7,
          stream: false,
        },
        {
          headers: {
            'Authorization': `Bearer ${this.settings.apiKey}`,
            'Content-Type': 'application/json',
          },
          timeout: 30000, // 30 second timeout
        }
      );

      if (response.data && response.data.choices && response.data.choices[0]) {
        return response.data.choices[0].message.content;
      } else {
        throw new Error('Invalid response format from AI service');
      }
    } catch (error: any) {
      console.error('AI Service Error:', error);
      
      if (axios.isAxiosError(error)) {
        if (error.response) {
          // Server responded with error status
          const status = error.response.status;
          const message = error.response.data?.error?.message || error.response.data?.message || 'API request failed';
          
          if (status === 401) {
            throw new Error('Invalid API key. Please check your credentials.');
          } else if (status === 429) {
            throw new Error('Rate limit exceeded. Please try again later.');
          } else if (status === 500) {
            throw new Error('AI service is temporarily unavailable. Please try again later.');
          } else {
            throw new Error(`API Error (${status}): ${message}`);
          }
        } else if (error.request) {
          // Network error
          throw new Error('Network error. Please check your internet connection.');
        } else {
          // Request setup error
          throw new Error('Failed to send request to AI service.');
        }
      } else {
        // Non-axios error
        throw new Error(error.message || 'An unexpected error occurred.');
      }
    }
  }

  async testConnection(): Promise<boolean> {
    try {
      const testMessages: Message[] = [{
        id: 'test',
        content: 'Hello',
        role: 'user',
        timestamp: new Date(),
      }];
      
      await this.sendMessage(testMessages);
      return true;
    } catch (error) {
      console.error('Connection test failed:', error);
      return false;
    }
  }
}

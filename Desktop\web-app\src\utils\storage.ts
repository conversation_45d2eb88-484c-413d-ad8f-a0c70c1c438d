import { ChatSettings, Message } from '../types';

const STORAGE_KEYS = {
  CHAT_SETTINGS: 'ai-chat-settings',
  CHAT_HISTORY: 'ai-chat-history',
};

export const storageUtils = {
  // Settings management
  saveSettings: (settings: ChatSettings): void => {
    try {
      localStorage.setItem(STORAGE_KEYS.CHAT_SETTINGS, JSON.stringify(settings));
    } catch (error) {
      console.error('Failed to save settings:', error);
    }
  },

  loadSettings: (): ChatSettings | null => {
    try {
      const stored = localStorage.getItem(STORAGE_KEYS.CHAT_SETTINGS);
      if (stored) {
        const settings = JSON.parse(stored);
        return {
          ...settings,
          // Provide defaults if missing
          apiProvider: settings.apiProvider || 'CHUTS AI',
          model: settings.model || 'deepseek-ai/DeepSeek-R1',
          apiKey: settings.apiKey || 'cpk_da2527fbe9ed4ce69de560d58c719aa5.0284016f1b955c9fba6d8eb4eb205731.PwPheBR15vbv71Y6aES6Ivqo4DVL3suA',
        };
      }
      return null;
    } catch (error) {
      console.error('Failed to load settings:', error);
      return null;
    }
  },

  // Chat history management
  saveChatHistory: (messages: Message[]): void => {
    try {
      const serializedMessages = messages.map(msg => ({
        ...msg,
        timestamp: msg.timestamp.toISOString(),
      }));
      localStorage.setItem(STORAGE_KEYS.CHAT_HISTORY, JSON.stringify(serializedMessages));
    } catch (error) {
      console.error('Failed to save chat history:', error);
    }
  },

  loadChatHistory: (): Message[] => {
    try {
      const stored = localStorage.getItem(STORAGE_KEYS.CHAT_HISTORY);
      if (stored) {
        const messages = JSON.parse(stored);
        return messages.map((msg: any) => ({
          ...msg,
          timestamp: new Date(msg.timestamp),
        }));
      }
      return [];
    } catch (error) {
      console.error('Failed to load chat history:', error);
      return [];
    }
  },

  clearChatHistory: (): void => {
    try {
      localStorage.removeItem(STORAGE_KEYS.CHAT_HISTORY);
    } catch (error) {
      console.error('Failed to clear chat history:', error);
    }
  },
};

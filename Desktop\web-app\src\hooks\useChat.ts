import { useState, useEffect, useCallback } from 'react';
import { Message, ChatSettings } from '../types';
import { AIService } from '../services/aiService';
import { storageUtils } from '../utils/storage';

export const useChat = () => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [settings, setSettings] = useState<ChatSettings>({
    apiProvider: 'CHUTS AI',
    model: 'deepseek-ai/DeepSeek-R1',
    apiKey: 'cpk_da2527fbe9ed4ce69de560d58c719aa5.0284016f1b955c9fba6d8eb4eb205731.PwPheBR15vbv71Y6aES6Ivqo4DVL3suA',
  });
  const [aiService, setAiService] = useState<AIService | null>(null);

  // Load settings and chat history on mount
  useEffect(() => {
    const savedSettings = storageUtils.loadSettings();
    if (savedSettings) {
      setSettings(savedSettings);
    }

    const savedMessages = storageUtils.loadChatHistory();
    setMessages(savedMessages);
  }, []);

  // Initialize AI service when settings change
  useEffect(() => {
    if (settings.apiKey) {
      const service = new AIService(settings);
      setAiService(service);
    }
  }, [settings]);

  // Save messages to storage whenever they change
  useEffect(() => {
    if (messages.length > 0) {
      storageUtils.saveChatHistory(messages);
    }
  }, [messages]);

  const updateSettings = useCallback((newSettings: ChatSettings) => {
    setSettings(newSettings);
    storageUtils.saveSettings(newSettings);
  }, []);

  const sendMessage = useCallback(async (content: string) => {
    if (!aiService) {
      setError('AI service not initialized. Please check your settings.');
      return;
    }

    if (!content.trim()) {
      return;
    }

    const userMessage: Message = {
      id: Date.now().toString(),
      content: content.trim(),
      role: 'user',
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    setIsLoading(true);
    setError(null);

    try {
      const updatedMessages = [...messages, userMessage];
      const response = await aiService.sendMessage(updatedMessages);

      const assistantMessage: Message = {
        id: (Date.now() + 1).toString(),
        content: response,
        role: 'assistant',
        timestamp: new Date(),
      };

      setMessages(prev => [...prev, assistantMessage]);
    } catch (err: any) {
      setError(err.message || 'Failed to send message');
      console.error('Send message error:', err);
    } finally {
      setIsLoading(false);
    }
  }, [aiService, messages]);

  const clearChat = useCallback(() => {
    setMessages([]);
    storageUtils.clearChatHistory();
    setError(null);
  }, []);

  const testConnection = useCallback(async (): Promise<boolean> => {
    if (!aiService) {
      return false;
    }

    try {
      setIsLoading(true);
      const result = await aiService.testConnection();
      if (!result) {
        setError('Connection test failed. Please check your API settings.');
      }
      return result;
    } catch (err: any) {
      setError(err.message || 'Connection test failed');
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [aiService]);

  return {
    messages,
    isLoading,
    error,
    settings,
    sendMessage,
    clearChat,
    updateSettings,
    testConnection,
    clearError: () => setError(null),
  };
};

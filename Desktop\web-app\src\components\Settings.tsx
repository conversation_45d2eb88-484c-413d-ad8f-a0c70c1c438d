import React, { useState } from 'react';
import { Settings as SettingsIcon, X, Check, AlertCircle } from 'lucide-react';
import { ChatSettings } from '../types';

interface SettingsProps {
  settings: ChatSettings;
  onUpdateSettings: (settings: ChatSettings) => void;
  onTestConnection: () => Promise<boolean>;
  isLoading: boolean;
}

export const Settings: React.FC<SettingsProps> = ({
  settings,
  onUpdateSettings,
  onTestConnection,
  isLoading,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [formData, setFormData] = useState(settings);
  const [testResult, setTestResult] = useState<'success' | 'error' | null>(null);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onUpdateSettings(formData);
    setIsOpen(false);
  };

  const handleTestConnection = async () => {
    const success = await onTestConnection();
    setTestResult(success ? 'success' : 'error');
    setTimeout(() => setTestResult(null), 3000);
  };

  const handleInputChange = (field: keyof ChatSettings, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  if (!isOpen) {
    return (
      <button
        onClick={() => setIsOpen(true)}
        className="settings-button"
        title="Settings"
      >
        <SettingsIcon size={20} />
      </button>
    );
  }

  return (
    <div className="settings-overlay">
      <div className="settings-modal">
        <div className="settings-header">
          <h2>AI Chat Settings</h2>
          <button
            onClick={() => setIsOpen(false)}
            className="close-button"
          >
            <X size={20} />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="settings-form">
          <div className="form-group">
            <label htmlFor="apiProvider">API Provider</label>
            <input
              id="apiProvider"
              type="text"
              value={formData.apiProvider}
              onChange={(e) => handleInputChange('apiProvider', e.target.value)}
              placeholder="e.g., CHUTS AI"
              required
            />
          </div>

          <div className="form-group">
            <label htmlFor="model">Model</label>
            <input
              id="model"
              type="text"
              value={formData.model}
              onChange={(e) => handleInputChange('model', e.target.value)}
              placeholder="e.g., deepseek-ai/DeepSeek-R1"
              required
            />
          </div>

          <div className="form-group">
            <label htmlFor="apiKey">API Key</label>
            <input
              id="apiKey"
              type="password"
              value={formData.apiKey}
              onChange={(e) => handleInputChange('apiKey', e.target.value)}
              placeholder="Enter your API key"
              required
            />
          </div>

          <div className="form-group">
            <label htmlFor="baseUrl">Base URL (Optional)</label>
            <input
              id="baseUrl"
              type="url"
              value={formData.baseUrl || ''}
              onChange={(e) => handleInputChange('baseUrl', e.target.value)}
              placeholder="https://api.example.com"
            />
          </div>

          <div className="settings-actions">
            <button
              type="button"
              onClick={handleTestConnection}
              disabled={isLoading}
              className="test-button"
            >
              {isLoading ? 'Testing...' : 'Test Connection'}
              {testResult === 'success' && <Check size={16} className="test-success" />}
              {testResult === 'error' && <AlertCircle size={16} className="test-error" />}
            </button>

            <div className="button-group">
              <button
                type="button"
                onClick={() => setIsOpen(false)}
                className="cancel-button"
              >
                Cancel
              </button>
              <button
                type="submit"
                className="save-button"
              >
                Save Settings
              </button>
            </div>
          </div>
        </form>
      </div>
    </div>
  );
};
